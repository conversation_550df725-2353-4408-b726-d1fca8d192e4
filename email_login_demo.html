<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email <PERSON>gin <PERSON>mo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background:
                linear-gradient(135deg, rgba(255, 107, 107, 0.8) 0%, rgba(255, 142, 83, 0.8) 50%, rgba(255, 193, 7, 0.7) 100%),
                url('https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            background-repeat: no-repeat;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 2rem;
            position: relative;
        }

        /* Background Photo Controls */
        .bg-controls {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            display: flex;
            gap: 0.5rem;
            flex-direction: column;
        }

        .bg-control-btn {
            background: rgba(255, 255, 255, 0.9);
            border: none;
            padding: 0.8rem 1rem;
            border-radius: 25px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 600;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .bg-control-btn:hover {
            background: rgba(255, 255, 255, 1);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }

        .bg-control-btn.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        /* Background Image Overlay */
        .bg-image-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            opacity: 0;
            transition: opacity 0.5s ease;
            z-index: -1;
        }

        .bg-image-overlay.active {
            opacity: 0.3;
        }

        /* Extracted Image Display */
        .extracted-image-container {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) scale(0);
            max-width: 80vw;
            max-height: 80vh;
            z-index: 999;
            transition: transform 0.5s ease;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4);
        }

        .extracted-image-container.active {
            transform: translate(-50%, -50%) scale(1);
        }

        .extracted-image-container img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            display: block;
        }

        .extracted-image-controls {
            position: absolute;
            top: 15px;
            right: 15px;
            display: flex;
            gap: 0.5rem;
        }

        .extracted-control-btn {
            background: rgba(0, 0, 0, 0.7);
            color: white;
            border: none;
            width: 35px;
            height: 35px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background 0.3s ease;
        }

        .extracted-control-btn:hover {
            background: rgba(0, 0, 0, 0.9);
        }

        /* Photo Selection Modal */
        .photo-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1001;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .photo-modal.active {
            opacity: 1;
            visibility: visible;
        }

        .photo-modal-content {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .photo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        .photo-option {
            aspect-ratio: 1;
            background-size: cover;
            background-position: center;
            border-radius: 10px;
            cursor: pointer;
            transition: transform 0.3s ease;
            border: 3px solid transparent;
        }

        .photo-option:hover {
            transform: scale(1.05);
            border-color: #667eea;
        }
        
        .container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(15px);
            padding: 2.5rem;
            border-radius: 20px;
            box-shadow:
                0 25px 80px rgba(0, 0, 0, 0.4),
                0 0 0 1px rgba(255, 255, 255, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.3);
            width: 100%;
            max-width: 420px;
            animation: slideIn 0.6s ease-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(30px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }
        
        .tab-buttons {
            display: flex;
            margin-bottom: 2rem;
        }
        
        .tab-button {
            flex: 1;
            padding: 1rem;
            background: #f8f9fa;
            border: none;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .tab-button.active {
            background: linear-gradient(135deg, #ff6b6b 0%, #ff8e53 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
        }
        
        .tab-button:first-child {
            border-radius: 5px 0 0 5px;
        }
        
        .tab-button:last-child {
            border-radius: 0 5px 5px 0;
        }
        
        .form-container {
            display: none;
        }
        
        .form-container.active {
            display: block;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: #333;
            font-weight: 500;
        }
        
        .form-group input {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #ff6b6b;
            box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.1);
        }
        
        .btn {
            width: 100%;
            padding: 0.75rem;
            background: linear-gradient(135deg, #ff6b6b 0%, #ff8e53 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
            background: linear-gradient(135deg, #ff5252 0%, #ff7043 100%);
        }
        
        .message {
            padding: 0.75rem;
            border-radius: 5px;
            margin-bottom: 1rem;
            display: none;
        }
        
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .dashboard {
            display: none;
            text-align: center;
        }
        
        .dashboard.active {
            display: block;
        }
        
        .user-info {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 8px;
            margin: 1rem 0;
        }

        /* WhatsApp-like Chat Styles */
        .chat-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            margin: 2rem 0;
            overflow: hidden;
            max-width: 500px;
            margin-left: auto;
            margin-right: auto;
        }

        .chat-header {
            background: linear-gradient(135deg, #ff6b6b 0%, #ff8e53 100%);
            color: white;
            padding: 1rem 1.5rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .chat-header-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .chat-avatar {
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }

        .chat-title h4 {
            margin: 0;
            font-size: 1.1rem;
        }

        .chat-status {
            font-size: 0.8rem;
            opacity: 0.9;
        }

        .chat-actions {
            display: flex;
            gap: 0.5rem;
        }

        .chat-action-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            width: 35px;
            height: 35px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background 0.3s ease;
        }

        .chat-action-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .chat-messages {
            height: 300px;
            overflow-y: auto;
            padding: 1rem;
            background: #f0f2f5;
            background-image:
                radial-gradient(circle at 20% 50%, rgba(255, 107, 107, 0.05) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 142, 83, 0.05) 0%, transparent 50%);
        }

        .message-day {
            text-align: center;
            margin: 1rem 0;
        }

        .message-day span {
            background: rgba(255, 255, 255, 0.9);
            padding: 0.3rem 1rem;
            border-radius: 15px;
            font-size: 0.8rem;
            color: #666;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .message {
            margin-bottom: 1rem;
            display: flex;
        }

        .message.sent {
            justify-content: flex-end;
        }

        .message.received {
            justify-content: flex-start;
        }

        .message-content {
            max-width: 70%;
            padding: 0.8rem 1rem;
            border-radius: 18px;
            position: relative;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .message.sent .message-content {
            background: linear-gradient(135deg, #ff6b6b 0%, #ff8e53 100%);
            color: white;
            border-bottom-right-radius: 5px;
        }

        .message.received .message-content {
            background: white;
            color: #333;
            border-bottom-left-radius: 5px;
        }

        .message-content p {
            margin: 0 0 0.3rem 0;
            line-height: 1.4;
        }

        .message-time {
            font-size: 0.7rem;
            opacity: 0.7;
            float: right;
            margin-top: 0.2rem;
        }

        .chat-input-container {
            padding: 1rem;
            background: white;
            border-top: 1px solid #e9ecef;
            position: relative;
        }

        .chat-input-wrapper {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            background: #f8f9fa;
            border-radius: 25px;
            padding: 0.5rem;
        }

        .emoji-btn {
            background: none;
            border: none;
            font-size: 1.2rem;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 50%;
            transition: background 0.3s ease;
        }

        .emoji-btn:hover {
            background: rgba(255, 107, 107, 0.1);
        }

        #chat-input {
            flex: 1;
            border: none;
            background: none;
            padding: 0.8rem;
            font-size: 1rem;
            outline: none;
        }

        .send-btn {
            background: linear-gradient(135deg, #ff6b6b 0%, #ff8e53 100%);
            border: none;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .send-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }

        .emoji-picker {
            position: absolute;
            bottom: 100%;
            left: 1rem;
            background: white;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
            padding: 1rem;
            display: none;
            z-index: 1000;
        }

        .emoji-picker.active {
            display: block;
            animation: slideUp 0.3s ease;
        }

        .emoji-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 0.5rem;
        }

        .emoji-grid span {
            font-size: 1.5rem;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 8px;
            text-align: center;
            transition: background 0.3s ease;
        }

        .emoji-grid span:hover {
            background: #f0f2f5;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Profile Photo Styles */
        .profile-photo-container {
            text-align: center;
            margin-bottom: 1.5rem;
        }

        .profile-photo {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            object-fit: cover;
            border: 4px solid #667eea;
            cursor: pointer;
            transition: transform 0.3s ease;
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto;
            font-size: 3rem;
            color: #667eea;
        }

        .profile-photo:hover {
            transform: scale(1.05);
        }

        .profile-photo img {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            object-fit: cover;
        }

        .photo-upload-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9rem;
            margin-top: 0.5rem;
            transition: background 0.3s;
        }

        .photo-upload-btn:hover {
            background: #5a67d8;
        }

        .photo-preview {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid #667eea;
            margin: 0 auto 1rem;
            display: block;
        }

        .file-input {
            display: none;
        }

        .photo-options {
            display: flex;
            gap: 0.5rem;
            justify-content: center;
            margin-top: 1rem;
        }

        .photo-option-btn {
            background: #f8f9fa;
            border: 1px solid #ddd;
            padding: 0.5rem;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.8rem;
            transition: background 0.3s;
        }

        .photo-option-btn:hover {
            background: #e9ecef;
        }

        /* Avatar styles for different sizes */
        .avatar-small {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid #667eea;
        }

        .avatar-medium {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid #667eea;
        }

        .default-avatar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.2rem;
        }
    </style>
</head>
<body>
    <!-- Background Photo Controls -->
    <div class="bg-controls">
        <button class="bg-control-btn" onclick="toggleBackgroundPhoto()">
            🖼️ Background Photo
        </button>
        <button class="bg-control-btn" onclick="openPhotoSelector()">
            📸 Choose Photo
        </button>
        <button class="bg-control-btn" onclick="bringBackgroundToFront()">
            ⬆️ Bring to Front
        </button>
        <button class="bg-control-btn" onclick="resetBackground()">
            🔄 Reset
        </button>
    </div>

    <!-- Background Image Overlay -->
    <div class="bg-image-overlay" id="bg-overlay"></div>

    <!-- Extracted Image Container -->
    <div class="extracted-image-container" id="extracted-container">
        <div class="extracted-image-controls">
            <button class="extracted-control-btn" onclick="closeExtractedImage()" title="Close">
                ✕
            </button>
            <button class="extracted-control-btn" onclick="downloadExtractedImage()" title="Download">
                ⬇
            </button>
        </div>
    </div>

    <!-- Photo Selection Modal -->
    <div class="photo-modal" id="photo-modal">
        <div class="photo-modal-content">
            <h3 style="margin-bottom: 1rem; color: #333;">Choose Background Photo</h3>
            <div class="photo-grid">
                <div class="photo-option" style="background-image: url('https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80')" onclick="selectBackgroundPhoto('https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80')"></div>
                <div class="photo-option" style="background-image: url('https://images.unsplash.com/photo-1518837695005-2083093ee35b?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80')" onclick="selectBackgroundPhoto('https://images.unsplash.com/photo-1518837695005-2083093ee35b?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80')"></div>
                <div class="photo-option" style="background-image: url('https://images.unsplash.com/photo-1441974231531-c6227db76b6e?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80')" onclick="selectBackgroundPhoto('https://images.unsplash.com/photo-1441974231531-c6227db76b6e?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80')"></div>
                <div class="photo-option" style="background-image: url('https://images.unsplash.com/photo-1494790108755-2616c9c9b8d4?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80')" onclick="selectBackgroundPhoto('https://images.unsplash.com/photo-1494790108755-2616c9c9b8d4?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80')"></div>
                <div class="photo-option" style="background-image: url('https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80')" onclick="selectBackgroundPhoto('https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80')"></div>
                <div class="photo-option" style="background-image: url('https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80')" onclick="selectBackgroundPhoto('https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80')"></div>
            </div>
            <div style="margin-top: 1.5rem; text-align: center;">
                <button class="btn" onclick="closePhotoSelector()" style="background: #6c757d;">Close</button>
                <input type="file" id="custom-photo-input" accept="image/*" style="display: none;" onchange="handleCustomPhoto(this)">
                <button class="btn" onclick="document.getElementById('custom-photo-input').click()" style="margin-left: 1rem;">Upload Custom</button>
            </div>
        </div>
    </div>

    <div class="container">
        <div id="auth-section">
            <div id="message" class="message"></div>
            
            <div id="login-form" class="form-container active">
                <h2 style="color: #333; margin-bottom: 1.5rem; text-align: center;">Login with Email</h2>
                <form onsubmit="handleLogin(event)">
                    <div class="form-group">
                        <label for="login-email">Email Address</label>
                        <input type="email" id="login-email" required placeholder="Enter your email">
                    </div>
                    <div class="form-group">
                        <label for="login-password">Password</label>
                        <input type="password" id="login-password" required placeholder="Enter your password">
                    </div>
                    <button type="submit" class="btn">Login</button>
                </form>
            </div>
            
            <div id="register-form" class="form-container">
                <h2 style="color: #333; margin-bottom: 1.5rem; text-align: center;">Create Account</h2>
                <form onsubmit="handleRegister(event)">
                    <!-- Profile Photo Section -->
                    <div class="profile-photo-container">
                        <div class="profile-photo" id="register-photo-preview" onclick="document.getElementById('register-photo-input').click()">
                            📷
                        </div>
                        <button type="button" class="photo-upload-btn" onclick="document.getElementById('register-photo-input').click()">
                            Add Profile Photo
                        </button>
                        <input type="file" id="register-photo-input" class="file-input" accept="image/*" onchange="previewPhoto(this, 'register-photo-preview')">
                        <div class="photo-options">
                            <button type="button" class="photo-option-btn" onclick="removePhoto('register-photo-preview')">Remove</button>
                            <button type="button" class="photo-option-btn" onclick="setDefaultAvatar('register-photo-preview')">Default</button>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="register-username">Username</label>
                        <input type="text" id="register-username" required minlength="4" placeholder="Choose a username">
                    </div>
                    <div class="form-group">
                        <label for="register-email">Email Address</label>
                        <input type="email" id="register-email" required placeholder="Enter your email">
                    </div>
                    <div class="form-group">
                        <label for="register-password">Password</label>
                        <input type="password" id="register-password" required minlength="6" placeholder="Create a password">
                    </div>
                    <div class="form-group">
                        <label for="register-password2">Confirm Password</label>
                        <input type="password" id="register-password2" required placeholder="Confirm your password">
                    </div>
                    <button type="submit" class="btn">Create Account</button>
                </form>
            </div>

            <!-- Tab buttons moved to bottom -->
            <div class="tab-buttons">
                <button class="tab-button active" onclick="showTab('login')">Login</button>
                <button class="tab-button" onclick="showTab('register')">Register</button>
            </div>
        </div>
        
        <div id="dashboard" class="dashboard">
            <!-- Profile Photo Section -->
            <div class="profile-photo-container">
                <div class="profile-photo" id="dashboard-photo" onclick="document.getElementById('dashboard-photo-input').click()">
                    👤
                </div>
                <button type="button" class="photo-upload-btn" onclick="document.getElementById('dashboard-photo-input').click()">
                    Change Photo
                </button>
                <input type="file" id="dashboard-photo-input" class="file-input" accept="image/*" onchange="updateProfilePhoto(this)">
                <div class="photo-options">
                    <button type="button" class="photo-option-btn" onclick="removePhoto('dashboard-photo')">Remove</button>
                    <button type="button" class="photo-option-btn" onclick="setDefaultAvatar('dashboard-photo')">Default</button>
                </div>
            </div>

            <h2 style="color: #333; margin-bottom: 1rem;">Welcome to your Dashboard!</h2>
            <p style="color: #666; margin-bottom: 2rem;">Hello, <strong id="dashboard-username"></strong>! You are successfully logged in.</p>

            <div class="user-info">
                <h3 style="color: #333; margin-bottom: 1rem;">Account Information</h3>
                <p style="color: #666;"><strong>Username:</strong> <span id="user-info-username"></span></p>
                <p style="color: #666;"><strong>Email:</strong> <span id="user-info-email"></span></p>
                <p style="color: #666;"><strong>Status:</strong> Logged In</p>
            </div>

            <!-- WhatsApp-like Chat Interface -->
            <div class="chat-container">
                <div class="chat-header">
                    <div class="chat-header-info">
                        <div class="chat-avatar">💬</div>
                        <div class="chat-title">
                            <h4>Chat Room</h4>
                            <span class="chat-status">Online</span>
                        </div>
                    </div>
                    <div class="chat-actions">
                        <button class="chat-action-btn" onclick="clearChat()" title="Clear Chat">🗑️</button>
                        <button class="chat-action-btn" onclick="toggleChat()" title="Minimize">➖</button>
                    </div>
                </div>

                <div class="chat-messages" id="chat-messages">
                    <div class="message-day">
                        <span>Today</span>
                    </div>
                    <div class="message received">
                        <div class="message-content">
                            <p>Welcome to the chat! 👋</p>
                            <span class="message-time">12:00 PM</span>
                        </div>
                    </div>
                </div>

                <div class="chat-input-container">
                    <div class="chat-input-wrapper">
                        <button class="emoji-btn" onclick="toggleEmojiPicker()">😊</button>
                        <input type="text" id="chat-input" placeholder="Type a message..." onkeypress="handleChatKeyPress(event)">
                        <button class="send-btn" onclick="sendMessage()">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                            </svg>
                        </button>
                    </div>
                    <div class="emoji-picker" id="emoji-picker">
                        <div class="emoji-grid">
                            <span onclick="insertEmoji('😊')">😊</span>
                            <span onclick="insertEmoji('😂')">😂</span>
                            <span onclick="insertEmoji('❤️')">❤️</span>
                            <span onclick="insertEmoji('👍')">👍</span>
                            <span onclick="insertEmoji('👋')">👋</span>
                            <span onclick="insertEmoji('🔥')">🔥</span>
                            <span onclick="insertEmoji('💯')">💯</span>
                            <span onclick="insertEmoji('🎉')">🎉</span>
                        </div>
                    </div>
                </div>
            </div>

            <button onclick="logout()" class="btn" style="background: #dc3545; margin-top: 1rem;">Logout</button>
        </div>
    </div>

    <script>
        // Simple in-memory storage for demo purposes
        let users = JSON.parse(localStorage.getItem('emailUsers') || '{}');
        let currentUser = localStorage.getItem('currentEmailUser');

        // Check if user is already logged in
        if (currentUser) {
            const userData = users[currentUser];
            if (userData) {
                showDashboard(userData.username, currentUser);
            }
        }

        // Photo handling functions
        function previewPhoto(input, previewId) {
            const file = input.files[0];
            const preview = document.getElementById(previewId);

            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    preview.innerHTML = `<img src="${e.target.result}" alt="Profile photo">`;
                };
                reader.readAsDataURL(file);
            }
        }

        function removePhoto(previewId) {
            const preview = document.getElementById(previewId);
            if (previewId === 'register-photo-preview') {
                preview.innerHTML = '📷';
            } else {
                preview.innerHTML = '👤';
            }

            // Clear file input
            const fileInput = previewId === 'register-photo-preview' ?
                document.getElementById('register-photo-input') :
                document.getElementById('dashboard-photo-input');
            if (fileInput) fileInput.value = '';

            // Update user data if logged in
            if (currentUser && previewId === 'dashboard-photo') {
                users[currentUser].profilePhoto = null;
                localStorage.setItem('emailUsers', JSON.stringify(users));
            }
        }

        function setDefaultAvatar(previewId) {
            const preview = document.getElementById(previewId);
            const username = previewId === 'register-photo-preview' ?
                document.getElementById('register-username').value :
                users[currentUser]?.username;

            if (username) {
                const initial = username.charAt(0).toUpperCase();
                preview.innerHTML = `<div class="default-avatar" style="width: 100%; height: 100%; border-radius: 50%;">${initial}</div>`;

                // Update user data if logged in
                if (currentUser && previewId === 'dashboard-photo') {
                    users[currentUser].profilePhoto = 'default';
                    localStorage.setItem('emailUsers', JSON.stringify(users));
                }
            }
        }

        function updateProfilePhoto(input) {
            const file = input.files[0];
            if (file && currentUser) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const photoData = e.target.result;
                    users[currentUser].profilePhoto = photoData;
                    localStorage.setItem('emailUsers', JSON.stringify(users));

                    // Update preview
                    document.getElementById('dashboard-photo').innerHTML = `<img src="${photoData}" alt="Profile photo">`;
                };
                reader.readAsDataURL(file);
            }
        }

        function loadUserPhoto(email, photoElementId) {
            const userData = users[email];
            const photoElement = document.getElementById(photoElementId);

            if (userData && userData.profilePhoto) {
                if (userData.profilePhoto === 'default') {
                    const initial = userData.username.charAt(0).toUpperCase();
                    photoElement.innerHTML = `<div class="default-avatar" style="width: 100%; height: 100%; border-radius: 50%;">${initial}</div>`;
                } else {
                    photoElement.innerHTML = `<img src="${userData.profilePhoto}" alt="Profile photo">`;
                }
            } else {
                // Show default icon
                photoElement.innerHTML = photoElementId === 'register-photo-preview' ? '📷' : '👤';
            }
        }
        
        function showTab(tab) {
            // Update tab buttons
            document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            // Update form containers
            document.querySelectorAll('.form-container').forEach(container => container.classList.remove('active'));
            document.getElementById(tab + '-form').classList.add('active');
            
            // Clear message
            hideMessage();
        }
        
        function showMessage(text, type) {
            const messageEl = document.getElementById('message');
            messageEl.textContent = text;
            messageEl.className = `message ${type}`;
            messageEl.style.display = 'block';
        }
        
        function hideMessage() {
            document.getElementById('message').style.display = 'none';
        }
        
        function isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }
        
        function handleRegister(event) {
            event.preventDefault();

            const username = document.getElementById('register-username').value;
            const email = document.getElementById('register-email').value.toLowerCase();
            const password = document.getElementById('register-password').value;
            const password2 = document.getElementById('register-password2').value;

            if (!isValidEmail(email)) {
                showMessage('Please enter a valid email address', 'error');
                return;
            }

            if (password !== password2) {
                showMessage('Passwords do not match', 'error');
                return;
            }

            // Check if email already exists
            if (users[email]) {
                showMessage('Email already exists', 'error');
                return;
            }

            // Check if username already exists
            const existingUsername = Object.values(users).find(user => user.username === username);
            if (existingUsername) {
                showMessage('Username already exists', 'error');
                return;
            }

            // Get profile photo
            const photoPreview = document.getElementById('register-photo-preview');
            let profilePhoto = null;

            if (photoPreview.innerHTML.includes('<img')) {
                // Extract image src
                const img = photoPreview.querySelector('img');
                profilePhoto = img ? img.src : null;
            } else if (photoPreview.innerHTML.includes('default-avatar')) {
                profilePhoto = 'default';
            }

            users[email] = {
                username: username,
                password: password,
                email: email,
                profilePhoto: profilePhoto
            };
            localStorage.setItem('emailUsers', JSON.stringify(users));

            showMessage('Account created successfully! Please login with your email.', 'success');

            // Clear form and switch to login
            document.getElementById('register-form').reset();
            removePhoto('register-photo-preview');
            setTimeout(() => {
                document.querySelector('.tab-button').click();
            }, 1500);
        }
        
        function handleLogin(event) {
            event.preventDefault();
            
            const email = document.getElementById('login-email').value.toLowerCase();
            const password = document.getElementById('login-password').value;
            
            if (!isValidEmail(email)) {
                showMessage('Please enter a valid email address', 'error');
                return;
            }
            
            if (!users[email] || users[email].password !== password) {
                showMessage('Invalid email or password', 'error');
                return;
            }
            
            localStorage.setItem('currentEmailUser', email);
            showDashboard(users[email].username, email);
        }
        
        function showDashboard(username, email) {
            document.getElementById('auth-section').style.display = 'none';
            document.getElementById('dashboard').classList.add('active');
            document.getElementById('dashboard-username').textContent = username;
            document.getElementById('user-info-username').textContent = username;
            document.getElementById('user-info-email').textContent = email;

            // Load user's profile photo
            loadUserPhoto(email, 'dashboard-photo');
        }
        
        function logout() {
            localStorage.removeItem('currentEmailUser');
            document.getElementById('auth-section').style.display = 'block';
            document.getElementById('dashboard').classList.remove('active');
            document.getElementById('login-form').reset();
            showMessage('You have been logged out', 'success');
        }

        // Background Photo Functions
        let currentBackgroundImage = null;
        let backgroundActive = false;

        function toggleBackgroundPhoto() {
            const overlay = document.getElementById('bg-overlay');
            const btn = event.target;

            if (!currentBackgroundImage) {
                // Set default background if none selected
                currentBackgroundImage = 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80';
                overlay.style.backgroundImage = `url('${currentBackgroundImage}')`;
            }

            backgroundActive = !backgroundActive;
            overlay.classList.toggle('active', backgroundActive);
            btn.classList.toggle('active', backgroundActive);
            btn.textContent = backgroundActive ? '🖼️ Hide Background' : '🖼️ Background Photo';
        }

        function openPhotoSelector() {
            document.getElementById('photo-modal').classList.add('active');
        }

        function closePhotoSelector() {
            document.getElementById('photo-modal').classList.remove('active');
        }

        function selectBackgroundPhoto(imageUrl) {
            currentBackgroundImage = imageUrl;
            const overlay = document.getElementById('bg-overlay');
            overlay.style.backgroundImage = `url('${imageUrl}')`;

            if (!backgroundActive) {
                toggleBackgroundPhoto();
            }

            closePhotoSelector();
            showMessage('Background photo updated!', 'success');
        }

        function handleCustomPhoto(input) {
            const file = input.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    selectBackgroundPhoto(e.target.result);
                };
                reader.readAsDataURL(file);
            }
        }

        function bringBackgroundToFront() {
            if (!currentBackgroundImage) {
                showMessage('Please select a background photo first', 'error');
                return;
            }

            const container = document.getElementById('extracted-container');
            container.innerHTML = `
                <img src="${currentBackgroundImage}" alt="Extracted background image">
                <div class="extracted-image-controls">
                    <button class="extracted-control-btn" onclick="closeExtractedImage()" title="Close">
                        ✕
                    </button>
                    <button class="extracted-control-btn" onclick="downloadExtractedImage()" title="Download">
                        ⬇
                    </button>
                </div>
            `;

            container.classList.add('active');
            showMessage('Background photo brought to front!', 'success');
        }

        function closeExtractedImage() {
            document.getElementById('extracted-container').classList.remove('active');
        }

        function downloadExtractedImage() {
            if (currentBackgroundImage) {
                const link = document.createElement('a');
                link.href = currentBackgroundImage;
                link.download = 'extracted-background-image.jpg';
                link.click();
            }
        }

        function resetBackground() {
            const overlay = document.getElementById('bg-overlay');
            const container = document.getElementById('extracted-container');

            // Reset background
            overlay.classList.remove('active');
            overlay.style.backgroundImage = '';

            // Reset extracted image
            container.classList.remove('active');
            container.innerHTML = '';

            // Reset button states
            document.querySelectorAll('.bg-control-btn').forEach(button => {
                button.classList.remove('active');
                if (button.textContent.includes('Background') || button.textContent.includes('Hide')) {
                    button.textContent = '🖼️ Background Photo';
                }
            });

            currentBackgroundImage = null;
            backgroundActive = false;

            showMessage('Background reset successfully', 'info');
        }

        // Close modal when clicking outside
        document.getElementById('photo-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                closePhotoSelector();
            }
        });

        // Close extracted image when clicking outside
        document.getElementById('extracted-container').addEventListener('click', function(e) {
            if (e.target === this) {
                closeExtractedImage();
            }
        });

        // WhatsApp Chat Functions
        function sendMessage() {
            const input = document.getElementById('chat-input');
            const message = input.value.trim();

            if (message) {
                addMessage(message, 'sent');
                input.value = '';

                // Auto-reply after 1 second
                setTimeout(() => {
                    const replies = [
                        "Thanks for your message! 😊",
                        "That's interesting! Tell me more.",
                        "I'm here to help! 👍",
                        "Great to hear from you! 🎉",
                        "How can I assist you today?",
                        "Nice! Keep the conversation going! 💬"
                    ];
                    const randomReply = replies[Math.floor(Math.random() * replies.length)];
                    addMessage(randomReply, 'received');
                }, 1000);
            }
        }

        function addMessage(text, type) {
            const messagesContainer = document.getElementById('chat-messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;

            const currentTime = new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});

            messageDiv.innerHTML = `
                <div class="message-content">
                    <p>${text}</p>
                    <span class="message-time">${currentTime}</span>
                </div>
            `;

            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        function handleChatKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }

        function toggleEmojiPicker() {
            const picker = document.getElementById('emoji-picker');
            picker.classList.toggle('active');
        }

        function insertEmoji(emoji) {
            const input = document.getElementById('chat-input');
            input.value += emoji;
            input.focus();
            document.getElementById('emoji-picker').classList.remove('active');
        }

        function clearChat() {
            const messagesContainer = document.getElementById('chat-messages');
            messagesContainer.innerHTML = `
                <div class="message-day">
                    <span>Today</span>
                </div>
                <div class="message received">
                    <div class="message-content">
                        <p>Chat cleared! Start a new conversation 👋</p>
                        <span class="message-time">${new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}</span>
                    </div>
                </div>
            `;
        }

        function toggleChat() {
            const chatMessages = document.querySelector('.chat-messages');
            const chatInput = document.querySelector('.chat-input-container');

            if (chatMessages.style.display === 'none') {
                chatMessages.style.display = 'block';
                chatInput.style.display = 'block';
            } else {
                chatMessages.style.display = 'none';
                chatInput.style.display = 'none';
            }
        }

        // Close emoji picker when clicking outside
        document.addEventListener('click', function(e) {
            const picker = document.getElementById('emoji-picker');
            const emojiBtn = document.querySelector('.emoji-btn');

            if (picker && !picker.contains(e.target) && e.target !== emojiBtn) {
                picker.classList.remove('active');
            }
        });
    </script>
</body>
</html>
